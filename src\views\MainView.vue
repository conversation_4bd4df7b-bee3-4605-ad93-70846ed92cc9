<template>
  <div class="main-container">
    <!-- 动态背景元素 -->
    <div class="bg-animation">
      <div class="floating-cross" v-for="n in 8" :key="n" :style="{animationDelay: n * 0.6 + 's'}">+</div>
      <div class="floating-circle" v-for="n in 6" :key="n" :style="{animationDelay: n * 0.8 + 's'}"></div>
      <div class="floating-heart" v-for="n in 4" :key="n" :style="{animationDelay: n * 1.2 + 's'}">♥</div>
    </div>

    <!-- 主页内容 -->
    <div class="content-section">
      <!-- 欢迎卡片 -->
      <div class="welcome-card">
        <div class="welcome-icon">👋</div>
        <h1 class="welcome-title">健康概览</h1>
        <!-- <p class="welcome-subtitle">{{ formatDate(new Date()) }}</p> -->
      </div>

      <!-- 快速操作区域 -->
      <!-- <div class="quick-actions">
        <div class="action-item" @click="navigateTo('/indexView')">
          <div class="action-icon">📊</div>
          <span>查询指标</span>
        </div>
        <div class="action-item" @click="navigateTo('/reportView')">
          <div class="action-icon">📋</div>
          <span>体检报告</span>
        </div>
        <div class="action-item" @click="navigateTo('/examReportView')">
          <div class="action-icon">🧪</div>
          <span>检查报告</span>
        </div>
      </div> -->

      <!-- 最新血常规指标 -->
      <div class="overview-section" v-if="bloodTestData.length > 0">
        <div class="section-header">
          <h3 class="section-title">最新血常规</h3>
          <van-button
            type="primary"
            size="mini"
            @click="navigateTo('/indexView')"
            plain
          >
            查看更多
          </van-button>
        </div>
        <div class="indicators-grid">
          <div
            v-for="item in bloodTestData"
            :key="item.index_name"
            class="indicator-card"
            @click="goToIndexDetail(item.index_name)"
          >
            <div class="indicator-name">{{ item.index_name }}</div>
            <div class="indicator-value" :class="getStatusClass(item.index_status)">
              {{ item.index_value }}
              <span class="indicator-unit">{{ item.index_unit }}</span>
            </div>
            <div class="indicator-date">{{ formatDate(item.latest_date) }}</div>
          </div>
        </div>
      </div>

      <!-- 最新肿瘤指标 -->
      <div class="overview-section" v-if="tumorMarkersData.length > 0">
        <div class="section-header">
          <h3 class="section-title">最新肿瘤指标</h3>
          <van-button
            type="primary"
            size="mini"
            @click="navigateTo('/indexView')"
            plain
          >
            查看更多
          </van-button>
        </div>
        <div class="indicators-grid">
          <div
            v-for="item in tumorMarkersData"
            :key="item.index_name"
            class="indicator-card"
            @click="goToIndexDetail(item.index_name)"
          >
            <div class="indicator-name">{{ item.index_name }}</div>
            <div class="indicator-value" :class="getStatusClass(item.index_status)">
              {{ item.index_value }}
              <span class="indicator-unit">{{ item.index_unit }}</span>
            </div>
            <div class="indicator-date">{{ formatDate(item.latest_date) }}</div>
          </div>
        </div>
      </div>

      <!-- 最新CT报告 -->
      <div class="overview-section" v-if="ctReportData">
        <div class="section-header">
          <h3 class="section-title">最新CT报告</h3>
          <van-button
            type="primary"
            size="mini"
            @click="navigateTo('/examReportView')"
            plain
          >
            查看更多
          </van-button>
        </div>
        <div class="report-card" @click="goToCTReport(ctReportData.id)">
          <div class="report-header">
            <div class="report-title">{{ ctReportData.exam_type || 'CT检查' }}</div>
            <div class="report-date">{{ formatDate(ctReportData.medical_date) }}</div>
          </div>
          <div class="report-hospital">{{ ctReportData.hospital || '未知医院' }}</div>
          <div class="report-summary" v-if="ctReportData.exam_info">
            <van-text-ellipsis
              :content="ctReportData.exam_info"
              :rows="2"
              expand-text="展开"
              collapse-text="收起"
            />
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div class="empty-state" v-if="!hasAnyData">
        <div class="empty-icon">📊</div>
        <div class="empty-title">暂无数据</div>
        <div class="empty-subtitle">开始记录您的健康数据吧</div>
        <van-button
          type="primary"
          size="small"
          @click="navigateTo('/indexView')"
          style="margin-top: 16px;"
        >
          查看指标
        </van-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { showToast } from 'vant';
import { getMedicalIndexValue, getLatestCTReport } from '@/api/medical';

const router = useRouter();

// 响应式数据
const bloodTestData = ref([]);
const tumorMarkersData = ref([]);
const ctReportData = ref(null);
const loading = ref(false);


// 计算属性
const hasAnyData = computed(() => {
  return bloodTestData.value.length > 0 ||
         tumorMarkersData.value.length > 0 ||
         ctReportData.value !== null;
});

// 方法
const navigateTo = (path) => {
  router.push(path);
};

const goToIndexDetail = (indexName) => {
  router.push({
    name: 'indexDetail',
    query: {
      indexName: indexName,
      title: indexName
    }
  });
};

const goToCTReport = (reportId) => {
  router.push({
    name: 'examReportView',
    query: { reportId: reportId }
  });
};

const formatDate = (date) => {
  if (!date) return '';
  const d = new Date(date);
  return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`;
};

const getStatusClass = (status) => {
  return {
    'status-normal': status === 'normal',
    'status-high': status === 'high',
    'status-low': status === 'low',
    'status-abnormal': status === 'abnormal'
  };
};

// 获取最新血常规数据
const fetchBloodTestData = async () => {
  try {
    const params = {
      medical_type: 2, // 传递选中的类型ID
      limit: 5
    };

    const response = await getMedicalIndexValue(params);
    if (response.data.data && Array.isArray(response.data.data)) {
      bloodTestData.value = response.data.data; // 只显示前4个
    }
  } catch (error) {
    console.error('获取血常规数据失败:', error);
  }
};

// 获取最新肿瘤指标数据
const fetchTumorMarkersData = async () => {
  try {
    const params = {
      medical_type: 4, // 传递选中的类型ID
      limit: 5
    };

    const response = await getMedicalIndexValue(params);
    if (response.data.data && Array.isArray(response.data.data)) {
      tumorMarkersData.value = response.data.data; // 只显示前4个
    }
  } catch (error) {
    console.error('获取肿瘤指标数据失败:', error);
  }
};

// 获取最新CT报告数据
const fetchCTReportData = async () => {
  try {
    const response = await getLatestCTReport();
    if (response.data && response.data.length > 0) {
      ctReportData.value = response.data[0]; // 获取最新的一个
    }
  } catch (error) {
    console.error('获取CT报告数据失败:', error);
  }
};

// 初始化数据
const initData = async () => {
  loading.value = true;
  try {
    await Promise.all([
      fetchBloodTestData(),
      fetchTumorMarkersData(),
      fetchCTReportData()
    ]);
  } catch (error) {
    showToast('加载数据失败');
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  initData();
});
</script>

<style scoped>
.main-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  position: relative;
  overflow-x: hidden;
  padding-bottom: 60px; /* 为底部导航栏留出空间 */
}

/* 动态背景元素样式 - 与HomeView保持一致 */
.bg-animation {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.floating-cross {
  position: absolute;
  color: rgba(255, 255, 255, 0.08);
  font-size: 20px;
  font-weight: bold;
  animation: float 8s ease-in-out infinite;
}

.floating-cross:nth-child(1) { top: 5%; left: 8%; }
.floating-cross:nth-child(2) { top: 15%; right: 12%; }
.floating-cross:nth-child(3) { top: 45%; left: 3%; }
.floating-cross:nth-child(4) { top: 65%; right: 8%; }
.floating-cross:nth-child(5) { top: 25%; left: 85%; }
.floating-cross:nth-child(6) { bottom: 35%; left: 15%; }
.floating-cross:nth-child(7) { bottom: 15%; right: 20%; }
.floating-cross:nth-child(8) { top: 80%; left: 70%; }

.floating-circle {
  position: absolute;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.08);
  border-radius: 50%;
  animation: pulse 6s ease-in-out infinite;
}

.floating-circle:nth-child(9) { top: 12%; left: 50%; }
.floating-circle:nth-child(10) { top: 55%; right: 25%; }
.floating-circle:nth-child(11) { bottom: 25%; left: 60%; }
.floating-circle:nth-child(12) { top: 35%; right: 5%; }
.floating-circle:nth-child(13) { bottom: 45%; left: 35%; }
.floating-circle:nth-child(14) { top: 75%; right: 45%; }

.floating-heart {
  position: absolute;
  color: rgba(255, 255, 255, 0.06);
  font-size: 18px;
  animation: heartbeat 4s ease-in-out infinite;
}

.floating-heart:nth-child(15) { top: 30%; left: 20%; }
.floating-heart:nth-child(16) { top: 60%; right: 15%; }
.floating-heart:nth-child(17) { bottom: 20%; left: 80%; }
.floating-heart:nth-child(18) { top: 85%; left: 40%; }

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-15px) rotate(180deg); }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 0.08; }
  50% { transform: scale(1.3); opacity: 0.15; }
}

@keyframes heartbeat {
  0%, 100% { transform: scale(1); }
  25% { transform: scale(1.1); }
  50% { transform: scale(1); }
  75% { transform: scale(1.05); }
}

/* 内容区域 */
.content-section {
  position: relative;
  z-index: 2;
  padding: 0 16px;
  padding-bottom: 80px;
}

/* 欢迎卡片 */
.welcome-card {
  background: rgba(255, 255, 255, 0.95);
  padding: 24px;
  border-radius: 20px;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  margin-bottom: 20px;
}

.welcome-icon {
  font-size: 32px;
  margin-bottom: 12px;
}

.welcome-title {
  color: #2c5aa0;
  font-size: 24px;
  font-weight: 700;
  margin: 8px 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.welcome-subtitle {
  color: #6b7280;
  font-size: 14px;
  margin: 0;
}

/* 快速操作区域 */
.quick-actions {
  display: flex;
  justify-content: space-around;
  background: rgba(255, 255, 255, 0.95);
  padding: 20px;
  border-radius: 16px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  margin-bottom: 20px;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: transform 0.2s ease;
  padding: 8px;
  border-radius: 12px;
}

.action-item:hover {
  transform: translateY(-2px);
  background: rgba(44, 90, 160, 0.1);
}

.action-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.action-item span {
  color: #374151;
  font-size: 12px;
  font-weight: 500;
}

/* 概览区域 */
.overview-section {
  background: rgba(255, 255, 255, 0.95);
  padding: 20px;
  border-radius: 16px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  margin-bottom: 16px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-title {
  color: #2c5aa0;
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

/* 指标网格 */
.indicators-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.indicator-card {
  background: #f8fafc;
  padding: 16px;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  cursor: pointer;
  transition: all 0.2s ease;
}

.indicator-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #2c5aa0;
}

.indicator-name {
  color: #374151;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
}

.indicator-value {
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 4px;
}

.indicator-unit {
  font-size: 12px;
  font-weight: 400;
  margin-left: 4px;
}

.indicator-date {
  color: #6b7280;
  font-size: 12px;
}

/* 状态颜色 */
.status-normal {
  color: #10b981;
}

.status-high {
  color: #ef4444;
}

.status-low {
  color: #f59e0b;
}

.status-abnormal {
  color: #ef4444;
}

/* 报告卡片 */
.report-card {
  background: #f8fafc;
  padding: 16px;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  cursor: pointer;
  transition: all 0.2s ease;
}

.report-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #2c5aa0;
}

.report-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.report-title {
  color: #2c5aa0;
  font-size: 16px;
  font-weight: 600;
}

.report-date {
  color: #6b7280;
  font-size: 12px;
}

.report-hospital {
  color: #374151;
  font-size: 14px;
  margin-bottom: 8px;
}

.report-summary {
  color: #6b7280;
  font-size: 14px;
  line-height: 1.4;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 40px 20px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.empty-title {
  color: #374151;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
}

.empty-subtitle {
  color: #6b7280;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .indicators-grid {
    grid-template-columns: 1fr;
  }

  .quick-actions {
    padding: 16px;
  }

  .action-icon {
    font-size: 20px;
  }

  .welcome-title {
    font-size: 20px;
  }
}

@media (max-width: 480px) {
  .main-container {
    padding: 10px;
  }

  .content-section {
    padding: 0 8px;
  }

  .welcome-card {
    padding: 20px;
  }

  .overview-section {
    padding: 16px;
  }

  .section-title {
    font-size: 16px;
  }

  .indicator-card {
    padding: 12px;
  }

  .indicator-value {
    font-size: 16px;
  }
}
</style>